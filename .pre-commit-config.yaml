repos:
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.16.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-toml
          - types-requests
          - types-defusedxml
          - types-PyYAML
          - httpx
          - authlib
          - pydantic
          - pydantic_settings
          - rich
          - segno
          - typer
          - cadcutils
          - questionary
          - jwt
        args: [--config-file=pyproject.toml]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        exclude: ^docs/*
      - id: end-of-file-fixer
        exclude: ^docs/*
      - id: check-case-conflict
      - id: check-json
      - id: check-yaml
        args: ['--unsafe']
      - id: mixed-line-ending
      - id: check-toml
      - id: pretty-format-json
      - id: check-symlinks
      - id: detect-private-key

  - repo: https://github.com/PyCQA/bandit
    rev: '1.8.3'
    hooks:
      - id: bandit
        args: ["--skip=B101"]

  - repo: local
    hooks:
    - id: radon
      name: radon
      entry: radon
      verbose: true
      language: python
      additional_dependencies: [radon]
      args: ["cc", "--min=C","--total-average","--exclude=tests/*.py"]

  - repo: https://github.com/gitleaks/gitleaks
    rev: v8.27.0
    hooks:
      - id: gitleaks

  - repo: https://github.com/PyCQA/bandit
    rev: '1.8.3'
    hooks:
      - id: bandit
        args: ["-c", "pyproject.toml"]
        additional_dependencies: ["bandit[toml]"]
  # Secret Detection
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        exclude: (^|/)uv\.lock$|^tests/
  # Python code formatting and linting with Ruff
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.12
    hooks:
      # Linter
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      # Formatter
      - id: ruff-format
  # Documentation checks
  - repo: https://github.com/econchick/interrogate
    rev: 1.7.0
    hooks:
      - id: interrogate
        args: [--config=pyproject.toml]

  # GitHub Actions workflow linting
  - repo: https://github.com/rhysd/actionlint
    rev: v1.7.7
    hooks:
      - id: actionlint

  # Check for Python version compatibility
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.20.0
    hooks:
      - id: pyupgrade
        args: [--py39-plus]
