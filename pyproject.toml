[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "skaha"
version = "1.7.0"
requires-python = ">=3.10"
description = "Python Client for CANFAR Science Platform"
license = { file = "LICENSE" }
readme = "README.md"
keywords = ["skaha", "canfar", "python", "client", "container", "platform", "api"]
authors = [
    { name = "Shiny Brar", email = "<EMAIL>" }
]
maintainers = [
    { name = "Shiny Brar", email = "<EMAIL>" }
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Console",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "Intended Audience :: Science/Research",
    "Intended Audience :: System Administrators",
    "Natural Language :: English",
    "Operating System :: MacOS :: MacOS X",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: Implementation :: CPython",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Scientific/Engineering :: Astronomy",
    "Topic :: Scientific/Engineering :: Physics",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "License :: OSI Approved :: GNU Affero General Public License v3 or later (AGPLv3+)",
]

dependencies = [
    "authlib>=1.6.0",
    "cadcutils>=1.5.4",
    "defusedxml>=0.7.1",
    "httpx[http2]>=0.28.1",
    "pydantic>=2.9.2",
    "pydantic-settings>=2.9.1",
    "pyjwt>=2.10.1",
    "questionary>=2.1.0",
    "rich>=13.9.4",
    "segno>=1.6.6",
    "toml>=0.10.2",
    "typer>=0.16.0",
]

[tool.uv]
dev-dependencies = [
    "bandit[toml]>=1.7.5",
    "mypy>=1.15.0",
    "pre-commit>=4.2.0",
    "pytest>=8.3.5",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=6.1.1",
    "pytest-mock>=3.14.1",
    "pytest-order>=1.3.0",
    "pytest-xdist>=3.7.0",
    "respx>=0.22.0",
    "ruff>=0.11.11",
    "ty>=0.0.1a10",
    "types-defusedxml>=0.7.0.20250516",
    "types-pyyaml>=6.0.12.20250516",
    "types-toml>=0.10.8.20240310",
    "vulture>=2.11",
]

[project.optional-dependencies]
docs = [
    "mkdocs>=1.6.0",
    "mkdocs-material>=9.5.41",
    "mkdocs-git-revision-date-localized-plugin>=1.2.9",
    "mkdocstrings[python]>=0.25.0",
    "mike>=2.1.3",
]

[project.urls]
Homepage = "https://shinybrar.github.io/skaha/"
Repository = "https://github.com/shinybrar/skaha"
Documentation = "https://shinybrar.github.io/skaha/"
Changelog = "https://shinybrar.github.io/skaha/changelog/"
Issues = "https://github.com/shinybrar/skaha/issues"

[project.scripts]
skaha = "skaha.cli.main:main"

# Ruff configuration - handles formatting, linting, and import sorting
[tool.ruff]
target-version = "py39"
line-length = 88
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "N",    # pep8-naming
    "D",    # pydocstyle
    "UP",   # pyupgrade
    "YTT",  # flake8-2020
    "ANN",  # flake8-annotations
    "ASYNC", # flake8-async
    "S",    # flake8-bandit
    "BLE",  # flake8-blind-except
    "FBT",  # flake8-boolean-trap
    "B",    # flake8-bugbear
    "A",    # flake8-builtins
    "COM",  # flake8-commas
    "C4",   # flake8-comprehensions
    "DTZ",  # flake8-datetimez
    "T10",  # flake8-debugger
    "DJ",   # flake8-django
    "EM",   # flake8-errmsg
    "EXE",  # flake8-executable
    "FA",   # flake8-future-annotations
    "ISC",  # flake8-implicit-str-concat
    "ICN",  # flake8-import-conventions
    "G",    # flake8-logging-format
    "INP",  # flake8-no-pep420
    "PIE",  # flake8-pie
    "T20",  # flake8-print
    "PYI",  # flake8-pyi
    "PT",   # flake8-pytest-style
    "Q",    # flake8-quotes
    "RSE",  # flake8-raise
    "RET",  # flake8-return
    "SLF",  # flake8-self
    "SLOT", # flake8-slots
    "SIM",  # flake8-simplify
    "TID",  # flake8-tidy-imports
    "TCH",  # flake8-type-checking
    "INT",  # flake8-gettext
    "ARG",  # flake8-unused-arguments
    "PTH",  # flake8-use-pathlib
    "ERA",  # eradicate
    "PD",   # pandas-vet
    "PGH",  # pygrep-hooks
    "PL",   # pylint
    "TRY",  # tryceratops
    "FLY",  # flynt
    "NPY",  # numpy
    "AIR",  # airflow
    "PERF", # perflint
    "FURB", # refurb
    "LOG",  # flake8-logging
    "RUF",  # ruff-specific rules
]

ignore = [
    "ANN401",  # Dynamically typed expressions (Any) are disallowed
    "D100",    # Missing docstring in public module
    "D104",    # Missing docstring in public package
    "D107",    # Missing docstring in `__init__`
    "D203",    # 1 blank line required before class docstring
    "D213",    # Multi-line docstring summary should start at the second line
    "FBT001",  # Boolean-typed positional argument in function definition
    "FBT002",  # Boolean default positional argument in function definition
    "PLR0913", # Too many arguments in function definition
    "PLR2004", # Magic value used in comparison
    "S101",    # Use of assert detected (allow in tests)
    "TRY003",  # Avoid specifying long messages outside the exception class
    "COM812",  # missing-trailing-comma
    "PERF203", # try-except inside loop
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401", "F403", "F405"]
"tests/**/*.py" = [
    "S101",    # Use of assert detected
    "ANN",     # Type annotations
    "D103",    # Missing docstring in public function
    "PLR2004", # Magic value used in comparison
    "S105",    # Possible hardcoded password
    "S106",    # Possible hardcoded password
    "S108",    # Probable insecure usage of temporary file/directory
]
"docs/**/*.py" = ["INP001"]

[tool.ruff.lint.isort]
known-first-party = ["skaha"]


[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.pylint]
max-args = 8

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = true
docstring-code-line-length = "dynamic"

# MyPy configuration
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_subclassing_any = true
disallow_untyped_calls = true
disallow_untyped_decorators = true
disallow_untyped_defs = true
ignore_missing_imports = false
implicit_reexport = false
no_implicit_optional = true
extra_checks = true
strict_equality = true
strict_optional = true
plugins = ["pydantic.mypy"]

[[tool.mypy.overrides]]
module = ["tests.*"]
ignore_errors = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "8.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=skaha",
    "--cov-report=term-missing:skip-covered",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80",
    "-n=auto",  # Enable parallel execution with pytest-xdist
    "--dist=loadfile",  # Use loadfile distribution to ensure tests in same file run in same worker (required for pytest-order)
]
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "unit: marks tests as unit tests",
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "order: marks tests that need to run in a specific order (will run sequentially)",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# Coverage configuration
[tool.coverage.run]
source = ["skaha"]
omit = [
    "tests/*",
    "setup.py",
    "*/migrations/*",
    "venv/*",
    "*/venv/*",
    ".tox/*",
    "*/site-packages/*",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if __name__ == .__main__.:",
    "raise NotImplementedError",
    "if TYPE_CHECKING:",
    "pass",
    "@(abc\\.)?abstractmethod",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "raise NotImplementedError",
    "if 0:",
    "if False:",
    "if TYPE_CHECKING:",
]
show_missing = true
skip_covered = false
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"

# Bandit security linter configuration
[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process_function

# Additional tool configurations for completeness
[tool.vulture]
min_confidence = 80
paths = ["skaha", "tests"]
exclude = ["venv/", ".venv/"]

[tool.interrogate]
ignore-init-method = true
ignore-init-module = false
ignore-magic = false
ignore-semiprivate = false
ignore-private = false
ignore-property-decorators = false
ignore-module = false
ignore-nested-functions = false
ignore-nested-classes = true
ignore-setters = false
fail-under = 80
exclude = ["setup.py", "docs", "build"]
ignore-regex = ["^get$", "^mock_.*", ".*BaseClass.*"]
verbose = 0
quiet = false
whitelist-regex = []
color = true
