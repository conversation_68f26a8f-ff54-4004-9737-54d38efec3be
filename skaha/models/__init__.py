"""Models package for Skaha API.

This package contains all the Pydantic models used throughout the Skaha API client.
The models are organized into separate modules for better maintainability:

- types: Common type definitions and constants (Kind, Status, View)
- session: Session-related models (CreateSpec, FetchSpec)
- registry: Registry and discovery-related models (IVOASearchConfig, SkahaServer, etc.)
- container: Container registry models (ContainerRegistry)
"""
